package copier

import (
	"fmt"
	"os"
	"strings"

	"bella/pkg/checksum"
	"bella/pkg/progress"
)

// OperationType defines the action to be performed (e.g., Co<PERSON>, Wipe).
type OperationType int

const (
	OpUnknown OperationType = iota
	OpCopy
	OpWipe
	OpVerify
	OpInfo
	OpList
)

// String makes OperationType implement the fmt.Stringer interface.
func (o OperationType) String() string {
	switch o {
	case OpCopy:
		return "Copy"
	case OpWipe:
		return "Wipe"
	case OpVerify:
		return "Verify"
	case OpInfo:
		return "Info"
	case OpList:
		return "List"
	default:
		return "Unknown"
	}
}

// Config holds all settings for an operation.
type Config struct {
	Operation OperationType

	// Common
	Input        string
	Output       string
	BlockSize    int
	Progress     bool
	DryRun       bool
	Threads      int
	ProgressChan chan<- progress.Info // Channel for UI progress updates

	// Copy specific
	Count              int
	Skip               int64
	Seek               int64
	Compression        string // "compress", "decompress", "auto", or "none"
	CompressionType    string // "gzip", etc.
	Checksum           string
	Verify             bool
	Sparse             bool
	Append             bool
	SkipBadSectors     bool
	UseCopyOffload     bool
	PreserveAttributes bool

	// Wipe specific
	WipeMode   string
	WipePasses int

	// Internal state
	isRecursive bool
	isUIMode    bool // Indicates if running in GUI mode vs CLI mode
}

// NewConfig creates a config with sensible default values.
func NewConfig() *Config {
	return &Config{
		BlockSize:      4 * 1024 * 1024,
		Count:          -1,
		Progress:       true,
		Threads:        1,
		WipePasses:     1,
		UseCopyOffload: true,
	}
}

// Validate checks for conflicting or invalid settings before execution.
func (c *Config) Validate() error {
	var conflicts []string

	// General input/output checks
	if c.Operation == OpCopy || c.Operation == OpVerify {
		if c.Input == "" || c.Output == "" {
			conflicts = append(conflicts, "this operation requires both an -input and an -output path")
		}
	}
	if c.Operation == OpWipe && c.Output == "" {
		conflicts = append(conflicts, "wipe operation requires an -output path")
	}

	// Flag compatibility checks
	if c.Append {
		if c.Seek != 0 {
			conflicts = append(conflicts, "-append cannot be used with -seek")
		}
		if c.Verify {
			conflicts = append(conflicts, "-append cannot be used with -verify. Please run as two separate steps.")
		}
	}
	if c.Compression != "none" && c.Compression != "auto" {
		if c.Sparse {
			conflicts = append(conflicts, "-sparse is incompatible with compression")
		}
		if c.Operation == OpVerify {
			conflicts = append(conflicts, "compression flags are not applicable to a verify-only operation")
		}
	}
	if c.WipeMode != "" {
		if c.Input != "" {
			conflicts = append(conflicts, "-wipe cannot be used with -input")
		}
		if c.WipeMode != "zero" && c.WipeMode != "random" {
			conflicts = append(conflicts, "wipe mode must be 'zero' or 'random'")
		}
	}

	// Checksum validation
	if c.Checksum != "" {
		if err := checksum.ValidateAlgorithm(c.Checksum); err != nil {
			conflicts = append(conflicts, fmt.Sprintf("invalid checksum algorithm: %v", err))
		}
		if c.Operation == OpWipe {
			conflicts = append(conflicts, "checksum cannot be used with wipe operations")
		}
	}

	// Check for directory copy
	if c.Operation == OpCopy && c.Input != "" {
		stat, err := os.Stat(c.Input)
		if err == nil && stat.IsDir() {
			c.isRecursive = true
		}
	}

	if len(conflicts) > 0 {
		return fmt.Errorf("option conflicts detected:\n- %s", strings.Join(conflicts, "\n- "))
	}

	return nil
}

// SetUIMode sets whether this config is for UI mode (true) or CLI mode (false)
func (c *Config) SetUIMode(isUI bool) {
	c.isUIMode = isUI
}

// IsDevice checks if a given path is likely a block device.
func (c *Config) IsDevice(path string) bool {
	return strings.HasPrefix(path, "/dev/")
}

// ShouldUseCopyOffload determines if we can use the fast kernel copy_file_range.
func (c *Config) ShouldUseCopyOffload() bool {
	if !c.UseCopyOffload || c.Operation != OpCopy || c.isRecursive {
		return false
	}
	if c.IsDevice(c.Input) || c.IsDevice(c.Output) || c.Sparse || c.SkipBadSectors || c.Append {
		return false
	}
	if c.Compression != "none" {
		return false
	}
	if c.Checksum != "" {
		return false
	}
	return true
}

// ShouldUseMultiStage determines if we need separate stages for copy, verify, or compress.
func (c *Config) ShouldUseMultiStage() bool {
	isCompress := c.Compression == "compress" || (c.Compression == "auto" && !strings.HasSuffix(c.Input, ".gz"))
	return c.Operation == OpCopy && (c.Verify || isCompress)
}
