package ui

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"bella/pkg/copier"
	"bella/pkg/device"
	"bella/pkg/progress"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// App is the main TUI application structure.
type App struct {
	app   *tview.Application
	pages *tview.Pages
}

// NewApp creates and initializes the TUI application.
func NewApp() *App {
	return &App{
		app:   tview.NewApplication(),
		pages: tview.NewPages(),
	}
}

// Run starts the application's main loop.
func (a *App) Run() error {
	mainMenu := a.createMainMenu()

	// Use a Frame for robust headers/footers.
	frame := tview.NewFrame(mainMenu).
		SetBorders(0, 0, 1, 1, 0, 0)

	// Add a header and a multi-part footer.
	frame.AddText("Bella - Advanced Data Copier", true, tview.AlignCenter, tcell.ColorAqua)

	sudoStatus := "[green]Running as root."
	if os.Getuid() != 0 {
		sudoStatus = "[yellow]Running as user. Restart with 'sudo' for device access."
	}
	frame.AddText(sudoStatus, false, tview.AlignLeft, tcell.ColorDefault)
	frame.AddText("Use arrows, Enter to select, Esc to go back", false, tview.AlignRight, tcell.ColorAqua)

	a.pages.AddPage("main", frame, true, true)
	a.app.SetRoot(a.pages, true).SetFocus(mainMenu)

	return a.app.Run()
}

// goBack is a helper to return to the main menu.
func (a *App) goBack(pageToHide string) {
	a.pages.RemovePage(pageToHide)
	a.pages.SwitchToPage("main")
	a.app.Sync()
	a.app.ForceDraw()
}

// switchToPage is a helper to cleanly switch between pages
func (a *App) switchToPage(pageName string) {
	a.pages.SwitchToPage(pageName)
	a.app.ForceDraw()
}

// createMainMenu builds the main navigation list.
func (a *App) createMainMenu() *tview.List {
	return tview.NewList().
		SetWrapAround(false).
		AddItem("Copy", "Copy files, directories, or devices", 'c', a.showCopyForm).
		AddItem("Verify", "Verify files by comparing source and destination", 'v', a.showVerifyForm).
		AddItem("Wipe", "Securely wipe a device or file", 'w', a.showWipeForm).
		AddItem("List Devices", "Show available storage devices (display only)", 'l', a.showDeviceList).
		AddItem("Quit", "Exit Bella", 'q', func() { a.app.Stop() })
}

// showCopyForm displays the form for copy operations.
func (a *App) showCopyForm() {
	pageName := "copyForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true) // Mark as GUI mode

	form := tview.NewForm().
		AddInputField("Input", "", 40, nil, nil).
		AddInputField("Output", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddInputField("Threads", "1", 5, nil, nil).
		AddInputField("Count", "-1", 10, nil, nil).
		AddInputField("Skip", "0", 10, nil, nil).
		AddInputField("Seek", "0", 10, nil, nil).
		AddDropDown("Compression", []string{"none", "compress", "decompress", "auto"}, 0, nil).
		AddDropDown("Compression Type", []string{"gzip"}, 0, nil).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil).
		AddCheckbox("Sparse Copy", false, nil).
		AddCheckbox("Skip Bad Sectors", false, nil).
		AddCheckbox("Verify After Copy", false, nil).
		AddCheckbox("Append Mode", false, nil).
		AddCheckbox("Disable Kernel Copy Offload", false, nil).
		AddCheckbox("Preserve Attributes", false, nil).
		AddCheckbox("Dry Run", false, nil)

	form.AddButton("Browse Input", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Output", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Copy", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showError(fmt.Sprintf("Auto block size failed: %v", err))
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showError(fmt.Sprintf("Invalid Block Size: %v", err))
				return
			}
			cfg.BlockSize = bs
		}

		threads, _ := strconv.Atoi(form.GetFormItem(3).(*tview.InputField).GetText())
		if threads < 1 {
			threads = 1
		}
		cfg.Threads = threads

		count, _ := strconv.Atoi(form.GetFormItem(4).(*tview.InputField).GetText())
		cfg.Count = count
		skip, _ := strconv.ParseInt(form.GetFormItem(5).(*tview.InputField).GetText(), 10, 64)
		cfg.Skip = skip
		seek, _ := strconv.ParseInt(form.GetFormItem(6).(*tview.InputField).GetText(), 10, 64)
		cfg.Seek = seek

		_, cfg.Compression = form.GetFormItem(7).(*tview.DropDown).GetCurrentOption()
		_, cfg.CompressionType = form.GetFormItem(8).(*tview.DropDown).GetCurrentOption()
		_, checksumAlgorithm := form.GetFormItem(9).(*tview.DropDown).GetCurrentOption()
		if checksumAlgorithm == "none" {
			cfg.Checksum = ""
		} else {
			cfg.Checksum = checksumAlgorithm
		}

		cfg.Sparse = form.GetFormItem(10).(*tview.Checkbox).IsChecked()
		cfg.SkipBadSectors = form.GetFormItem(11).(*tview.Checkbox).IsChecked()
		cfg.Verify = form.GetFormItem(12).(*tview.Checkbox).IsChecked()
		cfg.Append = form.GetFormItem(13).(*tview.Checkbox).IsChecked()
		cfg.UseCopyOffload = !form.GetFormItem(14).(*tview.Checkbox).IsChecked()
		cfg.PreserveAttributes = form.GetFormItem(15).(*tview.Checkbox).IsChecked()
		cfg.DryRun = form.GetFormItem(16).(*tview.Checkbox).IsChecked()

		if _, err := os.Stat(cfg.Output); err == nil && !cfg.Append {
			a.showFileExistsDialog(cfg.Output, func(action string) {
				switch action {
				case "overwrite":
					a.runOperation(cfg)
				case "append":
					cfg.Append = true
					a.runOperation(cfg)
				case "cancel":
				}
			})
		} else {
			a.runOperation(cfg)
		}
	})
	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Copy Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showVerifyForm displays the form for verify operations.
func (a *App) showVerifyForm() {
	pageName := "verifyForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true) // Mark as GUI mode

	form := tview.NewForm().
		AddInputField("Source", "", 40, nil, nil).
		AddInputField("Target", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil)

	form.AddButton("Browse Source", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Verify", func() {
		cfg.Operation = copier.OpVerify
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showError(fmt.Sprintf("Auto block size failed: %v", err))
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showError(fmt.Sprintf("Invalid Block Size: %v", err))
				return
			}
			cfg.BlockSize = bs
		}

		_, checksumAlgorithm := form.GetFormItem(3).(*tview.DropDown).GetCurrentOption()
		if checksumAlgorithm == "none" {
			cfg.Checksum = ""
		} else {
			cfg.Checksum = checksumAlgorithm
		}

		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })
	form.SetBorder(true).SetTitle(" Verify Files ")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showDeviceSelector displays a list of devices and calls onSelect with the chosen path.
func (a *App) showDeviceSelector(returnToPage string, onSelect func(path string)) {
	pageName := "deviceSelector"
	list := tview.NewList().SetWrapAround(false)
	list.SetBorder(true).SetTitle("Select a Device")

	devices, err := device.DetectDevices()
	if err != nil {
		a.showError(fmt.Sprintf("Could not detect devices: %v", err))
		return
	}

	if len(devices) == 0 {
		list.AddItem("No devices found", "", 0, nil)
	}

	for i, d := range devices {
		devicePath := d.Path
		sizeStr := progress.HumanizeBytes(uint64(d.Size))
		secondary := fmt.Sprintf("Size: %s, Model: %s", sizeStr, d.Model)
		func(path string) {
			list.AddItem(d.Path, secondary, rune('0'+i), func() {
				a.pages.RemovePage(pageName)
				onSelect(path)
			})
		}(devicePath)
	}

	list.AddItem("Cancel", "Return to previous screen", 'c', func() {
		a.pages.RemovePage(pageName)
		a.switchToPage(returnToPage)
	})

	list.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.pages.RemovePage(pageName)
			a.switchToPage(returnToPage)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, list, true, true)
}

// runOperation launches the progress screen and the backend logic.
func (a *App) runOperation(cfg *copier.Config) {
	pageName := "progressModal"

	// --- Create UI components ---
	modal := tview.NewFlex().SetDirection(tview.FlexRow)
	modal.SetBorder(true).SetTitle("Operation in Progress")

	stageText := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter)

	progressBar := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter)

	statsText := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter) // <-- FIX: Centered stats

	// --- Layout ---
	modal.AddItem(stageText, 1, 0, false).
		AddItem(progressBar, 3, 0, false).
		AddItem(statsText, 0, 1, false)

	// Use a Flex layout to center the modal and give it focus
	flex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(modal, 10, 0, true).
			AddItem(nil, 0, 1, false),
			80, 0, true). // <-- FIX: Set focus on this container
		AddItem(nil, 0, 1, false)

	// FIX: Set the input capture on the container that has focus
	flex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		// Default behavior is to do nothing until the operation is finished
		return event
	})

	a.pages.AddPage(pageName, flex, true, true)
	a.app.SetFocus(flex) // Explicitly set focus to the modal container

	// --- Progress Handling ---
	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	// Goroutine to update the UI from the progress channel
	go func() {
		for p := range progressChan {
			a.app.QueueUpdateDraw(func() {
				// FIX: Logic for displaying the final summary
				if p.IsFinished {
					modal.SetTitle("[green]Operation Finished")
					stageText.SetText(fmt.Sprintf("[green]✔ %s", "All Stages Complete"))
					progressBar.SetText(a.createProgressBar(100.0, "[green]"))
					// The summary from the reporter already has nice formatting
					statsText.SetText("\n" + p.Summary + "\n\n[yellow]Press any key to return.")
					// Now allow the user to exit
					flex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
						a.goBack(pageName)
						return nil
					})
					return // Stop processing updates for this operation
				}

				// --- In-progress updates ---
				stageText.SetText(fmt.Sprintf("[yellow]Stage: [white]%s", strings.Title(strings.ToLower(p.Stage))))
				progressBar.SetText(a.createProgressBar(p.PercentDone, "[cyan]"))
				stats := fmt.Sprintf(
					"[green]Speed:[white] %.2f MB/s\n[green]ETA:[white] %s\n[green]Data:[white] %s / %s",
					p.AvgSpeed, p.ETA,
					progress.HumanizeBytes(uint64(p.BytesDone)),
					progress.HumanizeBytes(uint64(p.BytesTotal)),
				)
				statsText.SetText(stats)
			})
		}
	}()

	// Goroutine to run the actual copy/wipe/verify operation
	go func() {
		defer close(progressChan) // Close channel when done to terminate UI goroutine loop
		err := copier.Execute(cfg)
		if err != nil {
			a.app.QueueUpdateDraw(func() {
				modal.SetTitle("[red]Operation Failed")
				stageText.SetText(fmt.Sprintf("[red]Error during stage: %s", strings.Title(strings.ToLower(cfg.Operation.String()))))
				progressBar.SetText("")
				statsText.SetText("\n" + err.Error() + "\n\n[yellow]Press any key to return.")
				// Allow the user to exit on error
				flex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
					a.goBack(pageName)
					return nil
				})
			})
		}
	}()
}

// createProgressBar is a new helper for the UI
func (a *App) createProgressBar(percent float64, color string) string {
	barWidth := 40
	filled := int(percent * float64(barWidth) / 100.0)
	if filled < 0 {
		filled = 0
	}
	if filled > barWidth {
		filled = barWidth
	}
	bar := fmt.Sprintf("%s%s[white:grey]%s", color, strings.Repeat("█", filled), strings.Repeat("░", barWidth-filled))
	return fmt.Sprintf("%s\n%.1f%%", bar, percent)
}

// showWipeForm displays the form for wipe operations.
func (a *App) showWipeForm() {
	pageName := "wipeForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true) // Mark as GUI mode

	form := tview.NewForm().
		AddInputField("Target Device", "", 40, nil, nil).
		AddDropDown("Mode", []string{"zero", "random"}, 1, nil).
		AddInputField("Passes", "1", 3, nil, nil).
		AddInputField("Block Size", "4M", 10, nil, nil)

	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			form.GetFormItem(3).(*tview.InputField).SetText("auto")
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Wipe", func() {
		cfg.Operation = copier.OpWipe
		cfg.Output = form.GetFormItem(0).(*tview.InputField).GetText()
		_, cfg.WipeMode = form.GetFormItem(1).(*tview.DropDown).GetCurrentOption()
		passes, _ := strconv.Atoi(form.GetFormItem(2).(*tview.InputField).GetText())
		if passes < 1 {
			passes = 1
		}
		cfg.WipePasses = passes

		bsText := form.GetFormItem(3).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Output)
			if err != nil {
				a.showError(fmt.Sprintf("Auto block size failed: %v", err))
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showError(fmt.Sprintf("Invalid Block Size: %v", err))
				return
			}
			cfg.BlockSize = bs
		}

		a.showConfirmation(fmt.Sprintf("This will IRREVERSIBLY DESTROY ALL DATA on %s.\nAre you sure?", cfg.Output), func() {
			a.runOperation(cfg)
		})
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Wipe Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showDeviceList displays a list of detected storage devices with detailed info on selection.
func (a *App) showDeviceList() {
	pageName := "deviceList"
	list := tview.NewList().SetWrapAround(false)
	list.SetBorder(true).SetTitle("Available Devices - Press Enter for details")

	devices, err := device.DetectDevices()
	if err != nil {
		a.showError(fmt.Sprintf("Could not detect devices: %v", err))
		return
	}

	if len(devices) == 0 {
		list.AddItem("No devices found", "", 0, nil)
	}

	for i, d := range devices {
		deviceInfo := d
		sizeStr := progress.HumanizeBytes(uint64(d.Size))
		secondary := fmt.Sprintf("Size: %s, Model: %s", sizeStr, d.Model)

		list.AddItem(d.Path, secondary, rune('0'+i), func() {
			a.showDeviceDetails(deviceInfo)
		})
	}

	list.AddItem("Back", "Return to main menu", 'b', func() { a.goBack(pageName) })

	list.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, list, true, true)
}

// showDeviceDetails displays detailed information about a device and its partitions
func (a *App) showDeviceDetails(deviceInfo device.Info) {
	pageName := "deviceDetails"
	mainFlex := tview.NewFlex().SetDirection(tview.FlexRow)
	mainFlex.SetBorder(true).SetTitle(fmt.Sprintf("Device Details: %s", deviceInfo.Path))

	detailInfo, err := device.GetDetailedDeviceInfo(deviceInfo.Path)
	if err != nil {
		a.showError(fmt.Sprintf("Could not get device details: %v", err))
		return
	}

	deviceInfoText := tview.NewTextView().SetDynamicColors(true)
	deviceInfoText.SetBorder(true).SetTitle("Device Information")

	var infoBuilder strings.Builder
	fmt.Fprintf(&infoBuilder, "[yellow]Device:[white] %s\n", deviceInfo.Path)
	fmt.Fprintf(&infoBuilder, "[yellow]Name:[white] %s\n", deviceInfo.Name)
	fmt.Fprintf(&infoBuilder, "[yellow]Size:[white] %s\n", progress.HumanizeBytes(uint64(deviceInfo.Size)))

	for key, value := range detailInfo {
		if key != "Size" {
			fmt.Fprintf(&infoBuilder, "[yellow]%s:[white] %s\n", key, value)
		}
	}
	deviceInfoText.SetText(infoBuilder.String())

	partitions, err := device.GetPartitions(deviceInfo.Name)
	if err != nil {
		a.showError(fmt.Sprintf("Could not get partitions: %v", err))
		return
	}

	partitionsList := tview.NewList().SetWrapAround(false)
	partitionsList.SetBorder(true).SetTitle("Partitions")

	if len(partitions) == 0 {
		partitionsList.AddItem("No partitions found", "", 0, nil)
	} else {
		for i, p := range partitions {
			sizeStr := progress.HumanizeBytes(uint64(p.Size))
			secondary := fmt.Sprintf("Size: %s", sizeStr)
			partitionsList.AddItem(p.Path, secondary, rune('0'+i), nil)
		}
	}

	partitionsList.AddItem("Back", "Return to device list", 'b', func() {
		a.pages.RemovePage(pageName)
		a.switchToPage("deviceList")
	})

	mainFlex.AddItem(deviceInfoText, 0, 1, false).
		AddItem(partitionsList, 0, 1, true)

	mainFlex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.pages.RemovePage(pageName)
			a.switchToPage("deviceList")
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, mainFlex, true, true)
}

// Helper functions
func (a *App) showError(message string) {
	pageName := "errorModal"
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"OK"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
		})
	a.pages.AddPage(pageName, modal, true, true)
}

func (a *App) showConfirmation(message string, onConfirm func()) {
	pageName := "confirmModal"
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"Yes", "No"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
			if buttonLabel == "Yes" {
				onConfirm()
			}
		})
	a.pages.AddPage(pageName, modal, true, true)
}

func (a *App) showFileExistsDialog(filePath string, onAction func(string)) {
	pageName := "fileExistsModal"
	message := fmt.Sprintf("File '%s' already exists.\nWhat would you like to do?", filePath)
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"Overwrite", "Append", "Cancel"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
			switch buttonLabel {
			case "Overwrite":
				onAction("overwrite")
			case "Append":
				onAction("append")
			case "Cancel":
				onAction("cancel")
			}
		})
	a.pages.AddPage(pageName, modal, true, true)
}

func parseBlockSize(s string) (int, error) {
	s = strings.ToUpper(strings.TrimSpace(s))
	mult := 1
	suffix := ""
	if strings.HasSuffix(s, "K") || strings.HasSuffix(s, "M") || strings.HasSuffix(s, "G") {
		suffix = s[len(s)-1:]
		s = s[:len(s)-1]
	}
	val, err := strconv.Atoi(s)
	if err != nil {
		return 0, err
	}
	switch suffix {
	case "K":
		mult = 1024
	case "M":
		mult = 1024 * 1024
	case "G":
		mult = 1024 * 1024 * 1024
	}
	return val * mult, nil
}

func (a *App) showDetailedError(operation string, err error) {
	pageName := "detailedError"
	text := tview.NewTextView().
		SetDynamicColors(true).
		SetText(fmt.Sprintf("[red]Error during %s:\n\n[white]%v", operation, err))

	modal := tview.NewModal().
		SetText(text.GetText(false)).
		AddButtons([]string{"OK"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
		})

	a.pages.AddPage(pageName, modal, true, true)
}

// RunCLIProgress creates a minimal tview-based progress display for CLI mode
func RunCLIProgress(cfg *copier.Config) error {
	app := tview.NewApplication()

	// Create progress display components
	stageText := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter)

	progressBar := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter)

	statsText := tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignCenter)

	// Create layout
	modal := tview.NewFlex().SetDirection(tview.FlexRow)
	modal.SetBorder(true).SetTitle("Operation in Progress")
	modal.AddItem(stageText, 1, 0, false).
		AddItem(progressBar, 3, 0, false).
		AddItem(statsText, 0, 1, false)

	// Center the modal
	flex := tview.NewFlex().
		AddItem(nil, 0, 1, false).
		AddItem(tview.NewFlex().SetDirection(tview.FlexRow).
			AddItem(nil, 0, 1, false).
			AddItem(modal, 10, 0, true).
			AddItem(nil, 0, 1, false),
			80, 0, true).
		AddItem(nil, 0, 1, false)

	// Set up progress channel
	progressChan := make(chan progress.Info, 100)
	cfg.ProgressChan = progressChan
	cfg.Progress = true

	// Track if operation is finished
	operationDone := make(chan error, 1)

	// Goroutine to update the UI from the progress channel
	go func() {
		for p := range progressChan {
			app.QueueUpdateDraw(func() {
				if p.IsFinished {
					modal.SetTitle("[green]Operation Finished")
					stageText.SetText(fmt.Sprintf("[green]✔ %s", "All Stages Complete"))
					progressBar.SetText(createProgressBarCLI(100.0, "[green]"))
					statsText.SetText("\n" + p.Summary + "\n\n[yellow]Press any key to exit.")

					// Allow user to exit
					flex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
						app.Stop()
						return nil
					})
					return
				}

				// In-progress updates
				stageText.SetText(fmt.Sprintf("[yellow]Stage: [white]%s", strings.Title(strings.ToLower(p.Stage))))
				progressBar.SetText(createProgressBarCLI(p.PercentDone, "[cyan]"))
				stats := fmt.Sprintf(
					"[green]Speed:[white] %.2f MB/s\n[green]ETA:[white] %s\n[green]Data:[white] %s / %s",
					p.AvgSpeed, p.ETA,
					progress.HumanizeBytes(uint64(p.BytesDone)),
					progress.HumanizeBytes(uint64(p.BytesTotal)),
				)
				statsText.SetText(stats)
			})
		}
	}()

	// Goroutine to run the actual operation
	go func() {
		defer close(progressChan)
		err := copier.Execute(cfg)
		operationDone <- err
		if err != nil {
			app.QueueUpdateDraw(func() {
				modal.SetTitle("[red]Operation Failed")
				stageText.SetText(fmt.Sprintf("[red]Error during stage: %s", strings.Title(strings.ToLower(cfg.Operation.String()))))
				progressBar.SetText("")
				statsText.SetText("\n" + err.Error() + "\n\n[yellow]Press any key to exit.")
				flex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
					app.Stop()
					return nil
				})
			})
		}
	}()

	// Set initial input capture (do nothing until operation finishes)
	flex.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		return event
	})

	app.SetRoot(flex, true).SetFocus(flex)

	// Run the app
	if err := app.Run(); err != nil {
		return fmt.Errorf("CLI progress display error: %w", err)
	}

	// Return the operation result
	return <-operationDone
}

// createProgressBarCLI creates a progress bar for CLI mode
func createProgressBarCLI(percent float64, color string) string {
	barWidth := 40
	filled := int(percent * float64(barWidth) / 100.0)
	if filled < 0 {
		filled = 0
	}
	if filled > barWidth {
		filled = barWidth
	}
	bar := fmt.Sprintf("%s%s[white:grey]%s", color, strings.Repeat("█", filled), strings.Repeat("░", barWidth-filled))
	return fmt.Sprintf("%s\n%.1f%%", bar, percent)
}
